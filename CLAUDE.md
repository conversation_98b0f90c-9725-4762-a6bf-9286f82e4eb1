# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development

```bash
# Start development server (dashboard package)
yarn dev
yarn dev

# Type checking
yarn test
yarn test:watch
```

### Building

```bash
# Build all packages
yarn build

# Build individual packages
yarn build:shared
yarn build:dashboard

# Development builds
yarn build:dev
yarn build:clean
```

### Testing

```bash
# Unit tests (Vitest)
yarn test
yarn test:watch
yarn test:coverage

# End-to-end tests (Playwright)
yarn test:e2e

# Component development (Storybook)
yarn storybook
yarn build-storybook
```

### Code Quality

```bash
# ESLint
yarn lint

# Code health analysis
yarn health
yarn analyze
yarn docs:check
yarn cleanup:legacy
```

### Maintenance

```bash
# Clean artifacts
yarn clean
yarn clean:deep

# Manage dependencies
yarn deps:check
yarn deps:sync
```

### Deployment

```bash
# Build for production
yarn build

# Preview production build
yarn preview
```

## Architecture

This is a **monorepo** for a React-based trading dashboard with a Formula 1 racing theme.

### Package Structure

- **`packages/shared/`** - Reusable components, hooks, theme system, and utilities
- **`packages/dashboard/`** - React application with feature-specific components

### Dependency Flow

```
shared → dashboard
```

### Key Architectural Patterns

**Atomic Design**

- Components organized as atoms → molecules → organisms → templates
- Located in `packages/shared/src/components/

**Feature-Based Organization**

- Features isolated in `packages/dashboard/src/features/
- Each feature contains: components, hooks, types, state, API services
- Examples: `trade-analysis`, `trade-journal`, `daily-guide`, `trading-dashboard`

**Path Aliases**

- `@adhd-trading-dashboard/shared` → `packages/shared/packages/dashboard/src/
- `@adhd-trading-dashboard/dashboard` → `packages/dashboard/packages/dashboard/src/

### Technical Stack

- **Framework**: React 18 + TypeScript
- **Styling**: styled-components with F1 racing theme
- **Build**: Vite (dev) + Webpack (production)
- **Testing**: Vitest (unit) + Playwright (E2E) + Storybook (components)
- **Package Management**: Yarn workspaces

### Special Considerations

**Theme System**

- F1 racing-inspired design language
- Consistent styling across components
- Performance-focused animations

**State Management**

- Context-based architecture
- Feature-specific state isolation
- Type-safe API layer with retry logic

### Code Health System

The repository includes comprehensive code analysis tools:

- Architecture analysis (`yarn analyze`)
- Documentation accuracy checking (`yarn docs:check`)
- Legacy tool cleanup (`yarn cleanup:legacy`)
- System health monitoring (`yarn health`)
- Always use Yarn commands for monorepo compatibility

### Build Configuration

- TypeScript with strict mode enabled
- Project references for optimal build performance
- Shared configuration across packages
- Bundle analysis available via `yarn analyze`

### Development Workflow

2. Start development: `yarn dev`
3. Run tests: `yarn test`
4. Architecture analysis: `yarn analyze`
5. System health check: `yarn health`
6. Build: `yarn build`

The codebase follows a disciplined monorepo architecture with clear separation of concerns, comprehensive tooling, and production-ready patterns.
