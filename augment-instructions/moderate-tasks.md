# Moderate Refactoring (1-3 hours)


## Split Large Components

**Priority**: medium  
**Estimated Time**: 2-3 hours  
**ID**: `split-large-components`

### Description
Break down overly large components into smaller, manageable pieces

### Context
Large components are harder to maintain and test

### Files Affected
- packages/shared/src/components/base.tsx
- packages/shared/src/components/organisms/DataCard.tsx
- packages/shared/src/components/molecules/TradeTableRow.tsx




### Instructions

## Split Large Components

### Context
These components are too large and should be broken down for better maintainability.

### Instructions for Augment AI:

For each large component:

1. **Analyze the component structure**:
   - Identify distinct responsibilities
   - Look for reusable UI patterns
   - Find state management that could be extracted

2. **Create split strategy**:
   - **UI Components**: Extract presentational parts
   - **Container Components**: Keep business logic
   - **Custom Hooks**: Extract state management

3. **Implementation approach**:
   - Start with extracting smallest reusable pieces
   - Create new files in appropriate folders (ui/, feature/, hooks/)
   - Update imports and exports
   - Maintain same functionality

### Example split:
```
// Before: LargeTradingDashboard.tsx (400 lines)

// After:
TradingDashboardContainer.tsx (business logic)
├── TradingDashboardView.tsx (presentation)
├── TradeList.tsx (reusable component)
├── TradeFilters.tsx (reusable component)
└── useTradingData.ts (custom hook)
```

### Components to split:
- **packages/shared/src/components/base.tsx**: 159 lines, complexity 25
- **packages/shared/src/components/organisms/DataCard.tsx**: 106 lines, complexity 19
- **packages/shared/src/components/molecules/TradeTableRow.tsx**: 289 lines, complexity 74


### Validation
Ensure split components maintain the same functionality

---


## Extract Custom Hooks

**Priority**: medium  
**Estimated Time**: 1-2 hours  
**ID**: `extract-custom-hooks`

### Description
Extract reusable state logic into custom hooks

### Context
Components have complex state logic that could be reused

### Files Affected
- packages/shared/src/components/base.tsx
- packages/shared/src/components/molecules/TradeTableRow.tsx
- packages/shared/src/components/molecules/TradeTableFilters.tsx
- packages/shared/src/components/molecules/TradeTable.tsx
- packages/shared/src/components/molecules/TradeTable.example.tsx




### Instructions

## Extract Custom Hooks

### Context
These components have complex state logic that could be extracted into reusable hooks.

### Instructions for Augment AI:

For each component:

1. **Identify extractable logic**:
   - State management patterns
   - Side effects (useEffect)
   - Complex calculations
   - API calls

2. **Create custom hook**:
   - Name with `use` prefix
   - Return object with state and actions
   - Include proper TypeScript types

3. **Update component**:
   - Replace inline logic with hook usage
   - Maintain same functionality
   - Improve readability

### Example extraction:
```typescript
// Before (in component):
const [data, setData] = useState([]);
const [loading, setLoading] = useState(false);
useEffect(() => {
  // complex data fetching logic
}, []);

// After (custom hook):
// useTradingData.ts
export const useTradingData = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  
  // extracted logic
  
  return { data, loading, refetch };
};

// In component:
const { data, loading, refetch } = useTradingData();
```

### Components for hook extraction:
- **packages/shared/src/components/base.tsx**: Complex state logic to extract
- **packages/shared/src/components/molecules/TradeTableRow.tsx**: Complex state logic to extract
- **packages/shared/src/components/molecules/TradeTableFilters.tsx**: Complex state logic to extract
- **packages/shared/src/components/molecules/TradeTable.tsx**: Complex state logic to extract
- **packages/shared/src/components/molecules/TradeTable.example.tsx**: Complex state logic to extract


### Validation
Test that extracted hooks work correctly in components

---


## Reorganize Component Structure

**Priority**: medium  
**Estimated Time**: 1-2 hours  
**ID**: `reorganize-components`

### Description
Move components to appropriate categorical folders

### Context
Components should be organized by type (UI, feature, layout)

### Files Affected
- packages/shared/src/styled.d.ts
- packages/dashboard/src/TestApp.tsx
- packages/dashboard/src/SimpleApp.tsx
- packages/dashboard/src/MinimalApp.tsx
- packages/dashboard/src/App.tsx




### Instructions

## Reorganize Component Structure

### Context
Components should be organized by their type and responsibility.

### Instructions for Augment AI:

1. **Categorize each component**:
   - **UI**: Reusable, presentational components (buttons, inputs, cards)
   - **Feature**: Business logic components tied to specific features
   - **Layout**: App structure (headers, sidebars, navigation)
   - **Pages**: Top-level route components

2. **Move to appropriate folders**:
   - `packages/dashboard/src/components/ui/` - Basic reusable components
   - `packages/dashboard/src/components/feature/` - Feature-specific components
   - `packages/dashboard/src/components/layout/` - Layout and navigation
   - `packages/dashboard/src/pages/` - Page components

3. **Update all imports**:
   - Find all files that import these components
   - Update import paths to new locations
   - Consider using path aliases (@/components/ui/Button)

4. **Create/update index files**:
   - Each category folder should export its components
   - Main components/index.ts should export all categories

### Organization example:
```
packages/dashboard/src/
├── components/
│   ├── ui/
│   │   ├── Button.tsx
│   │   ├── Input.tsx
│   │   └── index.ts
│   ├── feature/
│   │   ├── TradingDashboard.tsx
│   │   ├── TradeForm.tsx
│   │   └── index.ts
│   └── layout/
│       ├── Header.tsx
│       ├── Sidebar.tsx
│       └── index.ts
└── pages/
    ├── Dashboard.tsx
    └── Settings.tsx
```

### Components to reorganize:
- **packages/shared/src/styled.d.ts**: Move to appropriate category folder
- **packages/dashboard/src/TestApp.tsx**: Move to appropriate category folder
- **packages/dashboard/src/SimpleApp.tsx**: Move to appropriate category folder
- **packages/dashboard/src/MinimalApp.tsx**: Move to appropriate category folder
- **packages/dashboard/src/App.tsx**: Move to appropriate category folder
- **packages/shared/src/theme/ThemeProvider.tsx**: Move to appropriate category folder
- **packages/shared/src/state/createStoreContext.tsx**: Move to appropriate category folder
- **packages/shared/src/components/base.tsx**: Move to appropriate category folder


### Validation
Verify all imports still work after reorganization

---
