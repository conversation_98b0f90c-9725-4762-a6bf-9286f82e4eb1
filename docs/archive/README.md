# Documentation Archive

> **📚 Historical documentation preserved for reference**

This directory contains archived documentation that was moved due to extensive broken references or outdated content. The files are preserved for historical context but should not be used for current development.

## 📁 Archive Structure

### Legacy Documentation
- **legacy-docs/** - Old documentation with broken references
- **migration-guides/** - Historical migration documentation

### Task Instructions  
- **task-instructions/** - Outdated development task files
- **cleanup-reports/** - Historical cleanup and refactoring reports

## 📋 Archived Files

- `docs/migration/LEGACY_DOCS.md` → `docs/archive/legacy-docs/LEGACY_DOCS.md`
- `augment-instructions/comprehensive-tasks.md` → `docs/archive/task-instructions/comprehensive-tasks.md`
- `augment-instructions/immediate-tasks.md` → `docs/archive/task-instructions/immediate-tasks.md`
- `augment-instructions/moderate-tasks.md` → `docs/archive/task-instructions/moderate-tasks.md`
- `augment-instructions/quick-tasks.md` → `docs/archive/task-instructions/quick-tasks.md`
- `augment-instructions/TASK_SUMMARY.md` → `docs/archive/task-instructions/TASK_SUMMARY.md`
- `augment-instructions/individual-tasks/` → `docs/archive/task-instructions/individual-tasks`
- `TRADE_LOGGING_CLEANUP_COMPLETE.md` → `docs/archive/cleanup-reports/TRADE_LOGGING_CLEANUP_COMPLETE.md`
- `TRADING_ANALYSIS_CLEANUP_COMPLETE.md` → `docs/archive/cleanup-reports/TRADING_ANALYSIS_CLEANUP_COMPLETE.md`
- `IMPORT_FEATURE_INTEGRATION_GUIDE.md` → `docs/archive/cleanup-reports/IMPORT_FEATURE_INTEGRATION_GUIDE.md`

## ⚠️ Important Notes

- **Do not reference these files** in current documentation
- **Use current documentation** in the main `docs/` directory
- **Historical context only** - content may be severely outdated
- **Broken references expected** - files were archived due to accuracy issues

## 🔗 Current Documentation

For up-to-date documentation, see:

- **[Main Documentation](../README.md)** - Current documentation hub
- **[Getting Started](../GETTING_STARTED.md)** - Current setup guide  
- **[Architecture](../ARCHITECTURE.md)** - Current system design
- **[Development](../DEVELOPMENT.md)** - Current development workflow

---

**Archive Created:** 2025-05-28T08:30:39.729Z
**Reason:** Extensive broken references and outdated content
