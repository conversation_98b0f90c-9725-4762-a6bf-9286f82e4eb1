# Standardize Module Types (ESM/CommonJS)

**ID**: `fix-module-types`  
**Priority**: high  
**Estimated Time**: 20-30 minutes

## Context
Your project uses "type": "module" but some files still use CommonJS

## Description  
Convert mixed module usage to consistent ES modules

## Files to Modify
- server.js
- babel.config.js
- __mocks__/styleMock.js
- __mocks__/fileMock.js
- packages/shared/webpack.config.js




## Instructions for AI

## Standardize Module Types

### Context
Your project uses `"type": "module"` but some files still use CommonJS syntax.

### Instructions for Augment AI:

For each file with module type issues:

1. **Convert CommonJS to ES modules**:
   - Replace `require()` with `import`
   - Replace `module.exports` with `export`
   - Replace `exports.foo` with `export const foo`

2. **Update import syntax**:
   - Convert: `const foo = require('bar')` → `import foo from 'bar'`
   - Convert: `const { a, b } = require('bar')` → `import { a, b } from 'bar'`

3. **Update export syntax**:
   - Convert: `module.exports = foo` → `export default foo`
   - Convert: `exports.foo = bar` → `export const foo = bar`

### Example conversion:
```typescript
// Before (CommonJS)
const fs = require('fs');
const { glob } = require('glob');

function analyze() {
  // ...
}

module.exports = analyze;

// After (ES Module)
import fs from 'fs';
import { glob } from 'glob';

function analyze() {
  // ...
}

export default analyze;
```

### Files to convert:
- **server.js**: Uses CommonJS syntax in ES module project
- **babel.config.js**: Uses CommonJS syntax in ES module project
- **__mocks__/styleMock.js**: Uses CommonJS syntax in ES module project
- **__mocks__/fileMock.js**: Uses CommonJS syntax in ES module project
- **packages/shared/webpack.config.js**: Uses CommonJS syntax in ES module project
- **packages/dashboard/scripts/manage-assets.js**: Uses CommonJS syntax in ES module project
- **packages/dashboard/scripts/generate-logo-files.js**: Uses CommonJS syntax in ES module project
- **packages/dashboard/scripts/ensure-assets.js**: Uses CommonJS syntax in ES module project
- **packages/dashboard/public/assets/generate-placeholder-assets.js**: Uses CommonJS syntax in ES module project
- **packages/dashboard/public/assets/create-images.js**: Uses CommonJS syntax in ES module project


## Validation
Ensure all files use consistent import/export syntax

## Notes
- This is an automatically generated task based on codebase analysis
- Maintain existing functionality while implementing improvements
- Test thoroughly after making changes
- Update related documentation if necessary
