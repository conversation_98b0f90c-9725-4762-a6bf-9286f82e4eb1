# Organize Utility Functions

**ID**: `organize-utilities`  
**Priority**: medium  
**Estimated Time**: 30-45 minutes

## Context
Utilities are scattered across the codebase and need organization

## Description  
Move utility functions to organized folder structure

## Files to Modify
- vitest.setup.ts
- vitest.config.ts
- server.js
- playwright.config.ts
- cli.js




## Instructions for AI

## Organize Utility Functions

### Context
Utility functions are scattered across the codebase and should be organized by category.

### Instructions for Augment AI:

1. **Analyze each utility file**:
   - Determine the category (formatting, validation, calculations, api, etc.)
   - Check dependencies and exports

2. **Create organized structure**:
   - Move files to `packages/dashboard/src/utils/[category]/`
   - Categories: helpers, formatting, validation, calculations, api, constants

3. **Update imports**:
   - Find all files that import these utilities
   - Update import paths to new locations
   - Use relative paths or alias paths consistently

4. **Create category index files**:
   - Each category should have an index.ts
   - Export all utilities from that category

### Suggested organization:
```
packages/dashboard/src/utils/
├── formatting/
│   ├── currency.ts
│   ├── date.ts
│   └── index.ts
├── validation/
│   ├── schema.ts
│   ├── forms.ts
│   └── index.ts
└── calculations/
    ├── trading.ts
    └── index.ts
```

### Files to organize:
- **vitest.setup.ts**: Move to appropriate utils category
- **vitest.config.ts**: Move to appropriate utils category
- **server.js**: Move to appropriate utils category
- **playwright.config.ts**: Move to appropriate utils category
- **cli.js**: Move to appropriate utils category
- **babel.config.js**: Move to appropriate utils category
- **code-health/test-improvements.js**: Move to appropriate utils category
- **code-health/test-health-orchestrator.js**: Move to appropriate utils category
- **code-health/dynamic-scripts-cleanup.js**: Move to appropriate utils category
- **__mocks__/styleMock.js**: Move to appropriate utils category
- **__mocks__/fileMock.js**: Move to appropriate utils category
- **packages/shared/webpack.config.js**: Move to appropriate utils category
- **packages/shared/vite.config.ts**: Move to appropriate utils category
- **scripts/scripts.config.js**: Move to appropriate utils category
- **scripts/cli.js**: Move to appropriate utils category
- **packages/dashboard/src/simple-index.tsx**: Move to appropriate utils category
- **packages/dashboard/src/reportWebVitals.ts**: Move to appropriate utils category
- **packages/dashboard/src/index.tsx**: Move to appropriate utils category
- **packages/dashboard/src/devtools-config.js**: Move to appropriate utils category
- **packages/dashboard/dev-dist/sw.js**: Move to appropriate utils category
- **packages/dashboard/dev-dist/registerSW.js**: Move to appropriate utils category
- **packages/dashboard/scripts/manage-assets.js**: Move to appropriate utils category
- **packages/dashboard/scripts/generate-logo-files.js**: Move to appropriate utils category
- **packages/dashboard/scripts/ensure-assets.js**: Move to appropriate utils category
- **packages/shared/src/theme/lightTheme.ts**: Move to appropriate utils category
- **packages/shared/src/theme/index.ts**: Move to appropriate utils category
- **packages/shared/src/theme/f1Theme.ts**: Move to appropriate utils category
- **packages/shared/src/theme/darkTheme.ts**: Move to appropriate utils category
- **packages/shared/src/theme/GlobalStyles.tsx**: Move to appropriate utils category
- **packages/shared/src/state/createSelector.ts**: Move to appropriate utils category
- **packages/shared/src/hooks/index.ts**: Move to appropriate utils category
- **packages/shared/src/components/index.ts**: Move to appropriate utils category
- **packages/dashboard/src/routes/index.ts**: Move to appropriate utils category
- **packages/dashboard/src/layouts/index.ts**: Move to appropriate utils category
- **packages/shared/src/theme/variants/lightTheme.ts**: Move to appropriate utils category
- **packages/shared/src/theme/variants/index.ts**: Move to appropriate utils category
- **packages/shared/src/theme/variants/f1Theme.ts**: Move to appropriate utils category
- **packages/shared/src/theme/tokens/typography.ts**: Move to appropriate utils category
- **packages/shared/src/theme/tokens/spacing.ts**: Move to appropriate utils category
- **packages/shared/src/theme/tokens/index.ts**: Move to appropriate utils category
- **packages/shared/src/theme/tokens/colors.ts**: Move to appropriate utils category
- **packages/dashboard/public/assets/generate-placeholder-assets.js**: Move to appropriate utils category
- **packages/dashboard/public/assets/create-images.js**: Move to appropriate utils category
- **packages/shared/src/components/templates/index.ts**: Move to appropriate utils category
- **packages/shared/src/components/templates/DashboardTemplate.stories.tsx**: Move to appropriate utils category
- **packages/shared/src/components/organisms/index.ts**: Move to appropriate utils category
- **packages/shared/src/components/organisms/DataCard.stories.tsx**: Move to appropriate utils category
- **packages/shared/src/components/molecules/index.ts**: Move to appropriate utils category
- **packages/shared/src/components/molecules/Modal.stories.tsx**: Move to appropriate utils category
- **packages/shared/src/components/molecules/Card.stories.tsx**: Move to appropriate utils category
- **packages/shared/src/components/atoms/index.ts**: Move to appropriate utils category
- **packages/shared/src/components/atoms/Tag.stories.tsx**: Move to appropriate utils category
- **packages/shared/src/components/atoms/Badge.stories.tsx**: Move to appropriate utils category
- **packages/dashboard/src/features/trading-dashboard/index.ts**: Move to appropriate utils category
- **packages/dashboard/src/features/trade-journal/index.ts**: Move to appropriate utils category
- **packages/dashboard/src/features/trade-analysis/index.ts**: Move to appropriate utils category
- **packages/dashboard/src/features/settings/index.ts**: Move to appropriate utils category
- **packages/dashboard/src/features/performance-dashboard/index.ts**: Move to appropriate utils category
- **packages/dashboard/src/features/daily-guide/index.ts**: Move to appropriate utils category
- **packages/dashboard/src/components/molecules/ProfitLossCell.stories.tsx**: Move to appropriate utils category
- **packages/dashboard/src/features/trade-journal/constants/setupClassification.ts**: Move to appropriate utils category
- **packages/dashboard/src/features/trade-journal/constants/patternQuality.ts**: Move to appropriate utils category
- **packages/dashboard/src/features/trade-analysis/hooks/tradeAnalysisState.ts**: Move to appropriate utils category
- **packages/dashboard/src/features/daily-guide/state/index.ts**: Move to appropriate utils category
- **packages/dashboard/src/features/daily-guide/hooks/index.ts**: Move to appropriate utils category
- **packages/dashboard/src/features/daily-guide/components/index.ts**: Move to appropriate utils category
- **packages/dashboard/src/features/trade-journal/components/trade-list/index.ts**: Move to appropriate utils category
- **packages/dashboard/src/features/trade-journal/components/trade-journal/index.ts**: Move to appropriate utils category
- **packages/dashboard/src/features/trade-journal/components/trade-journal/TradeJournalContent.tsx**: Move to appropriate utils category
- **packages/dashboard/src/features/trade-journal/components/trade-form/index.ts**: Move to appropriate utils category
- **packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormTimingFields.tsx**: Move to appropriate utils category
- **packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormStrategyFields.tsx**: Move to appropriate utils category
- **packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormRiskFields.tsx**: Move to appropriate utils category
- **packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormMessages.tsx**: Move to appropriate utils category
- **packages/dashboard/src/features/trade-journal/components/trade-form/TradeFormBasicFields.tsx**: Move to appropriate utils category
- **packages/dashboard/src/features/daily-guide/components/ui/index.ts**: Move to appropriate utils category


## Validation
Verify all moved utilities are properly imported

## Notes
- This is an automatically generated task based on codebase analysis
- Maintain existing functionality while implementing improvements
- Test thoroughly after making changes
- Update related documentation if necessary
