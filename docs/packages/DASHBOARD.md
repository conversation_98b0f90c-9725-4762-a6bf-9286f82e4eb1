# Dashboard Package Documentation

> **Main application layer for trading features and user interface**

## Overview

The `dashboard` package is the user-facing React application for the ADHD Trading Dashboard. It implements all trading features, pages, layouts, and business logic, leveraging the shared package for UI, state, and API.

## 📦 Structure

```
packages/dashboard/src/
├── features/    # Feature-based organization (trade-analysis, trade-journal, daily-guide)
├── layouts/     # Application layouts
├── pages/       # Route-level components
└── routes/      # Route definitions and configuration
```

## 🚀 Features

- **Dashboard**: Overview of trading performance and key metrics
- **Daily Guide**: Daily trading plan and market overview
- **Trade Journal**: Record and review trades
- **Trade Analysis**: Analyze trading performance and patterns
- **Settings**: Configure dashboard settings

## 🧑‍💻 Usage

### Development

```bash
# Start the development server

# Build for production

# Run tests
```

### Importing Components

```tsx
import { Dashboard, DailyGuide } from "@adhd-trading-dashboard/dashboard";

<Dashboard />
<DailyGuide />
```

## 🏆 Best Practices

- Organize new features under `packages/dashboard/src/features/ using the feature module template
- Use only shared components/hooks for UI and logic
- Keep business logic and API integration within feature modules

---

For more details, see the [Architecture Guide](../ARCHITECTURE.md) and [Project Overview](../PROJECT_OVERVIEW.md).
