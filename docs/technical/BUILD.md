# Build System Documentation

> **🔧 Complete guide to building and deploying the ADHD Trading Dashboard**

## Overview

The ADHD Trading Dashboard uses a modern build system optimized for monorepo development and web application distribution. This document covers all build processes, configurations, and deployment strategies.

## 🏗️ Build Architecture

### Project Structure

The build system is designed around a **two-package monorepo**:

- **shared**: Foundation components, utilities, and theme system
- **dashboard**: Main React application with trading features

### Dependency Chain

```
shared → dashboard
```

All packages must be built in dependency order to ensure proper linking and type checking.

## 🚀 Quick Start

### Prerequisites

- **Node.js v18 LTS** (recommended)
- **Yarn package manager**
- **Git** (for version control and deployment)

### Initial Setup

```bash
# Install dependencies

# Build all packages
yarn build

# Start development server
yarn dev
```

## 📋 Build Commands

### Development Commands

```bash
# Start development server (dashboard package)
yarn dev
yarn dev

# Type checking
yarn test
yarn test:watch
```

### Build Commands

```bash
# Build all packages in dependency order
yarn build

# Build individual packages
yarn build:shared
yarn build:dashboard

# Development builds (faster, no optimization)
yarn build:dev

# Clean build with fresh dependencies
yarn build:clean
```

### Quality Assurance

```bash
# Run all tests
yarn test
yarn test:watch
yarn test:coverage

# Linting and formatting
yarn lint
yarn lint:fix

# Code health analysis
yarn analyze
yarn cleanup:legacy
```

### Maintenance Commands

```bash
# Clean build artifacts
yarn clean

# Deep clean (including node_modules)
yarn clean:deep

# Check and align package versions
yarn deps:check
yarn align-versions
```

## 🔧 Build System Components

### TypeScript Configuration

The project uses **TypeScript project references** for optimal build performance:

```json
// Root tsconfig.json
{
  "references": [{ "path": "./packages/shared" }, { "path": "./packages/dashboard" }]
}
```

**Benefits:**

- Incremental compilation - only rebuild changed packages
- Cross-package type validation
- Faster development builds
- Better IDE support

### Package Configurations

Each package has its own TypeScript configuration:

```
packages/shared/tsconfig.json      # Shared package config
packages/dashboard/tsconfig.json   # Dashboard package config
```

### Babel Configuration

Babel handles code transformation with these presets and plugins:

**Presets:**

- `@babel/preset-env` - ES features and browser compatibility
- `@babel/preset-react` - JSX transformation
- `@babel/preset-typescript` - TypeScript support

**Plugins:**

- `babel-plugin-styled-components` - Styled-components optimization
- `@babel/plugin-proposal-class-properties` - Class properties
- `@babel/plugin-syntax-dynamic-import` - Dynamic imports
- `@babel/plugin-transform-runtime` - Runtime helpers

### Build Tools

- **Vite** - Fast development server with HMR
- **TypeScript** - Type checking and compilation
- **ESLint** - Code quality and consistency
- **Prettier** - Code formatting
- **Vitest** - Unit testing framework
- **Playwright** - End-to-end testing

## 📦 Package Build Process

### Shared Package Build

The shared package is built first as it's a dependency for the dashboard:

```bash
# TypeScript compilation
tsc --build packages/shared/tsconfig.json

# Babel transformation
babel src --out-dir dist --extensions .ts,.tsx

# Declaration file generation
tsc --emitDeclarationOnly
```

**Output:**

- `dist/` - Compiled JavaScript files
- `dist/types/` - TypeScript declaration files
- `package.json` updates for proper exports

### Dashboard Package Build

The dashboard package depends on the shared package being built first:

```bash
# Vite production build
vite build

# Type checking
tsc --noEmit
```

**Output:**

- `dist/` - Optimized production bundle
- `dist/assets/` - Static assets (images, fonts, etc.)
- Source maps for debugging

## 🎯 Build Optimization

### Code Splitting

The build system implements several code splitting strategies:

1. **Route-based splitting** - Each page loads independently
2. **Feature-based splitting** - Trading features load on demand
3. **Component splitting** - Heavy components load when needed
4. **Vendor splitting** - Third-party libraries bundled separately

### Bundle Analysis

Analyze bundle size and composition:

```bash
# Generate bundle analysis
yarn analyze

# View detailed bundle report
yarn build && npx vite-bundle-analyzer dist
```

### Performance Optimizations

- **Tree shaking** - Remove unused code
- **Minification** - Compress JavaScript and CSS
- **Asset optimization** - Compress images and fonts
- **Gzip compression** - Server-side compression
- **Caching** - Optimize browser caching with hashed filenames

## 🚀 Deployment

### Static Site Deployment

Deploy as a modern web application:

```bash
# Build production bundle
yarn build

# Deploy to GitHub Pages

# Deploy to Netlify/Vercel
yarn build
# Upload packages/dashboard/dist/ to hosting platform

# Deploy to custom server
yarn build && rsync -av packages/dashboard/dist/ user@server:/path/to/webroot/
```

**Deployment Optimizations:**

- Modern ES modules for better performance
- Efficient code splitting and lazy loading
- Optimized asset compression
- Browser caching with hashed filenames

### Static Site Generation

Generate static files for deployment:

```bash
# Build static site
yarn build:static

# Preview production build locally
yarn preview
```

## 🧪 Testing in Build Pipeline

### Test Types

1. **Unit Tests** - Component and utility testing
2. **Integration Tests** - Feature workflow testing
3. **E2E Tests** - Complete user journey testing
4. **Visual Tests** - Component appearance testing

### Build Integration

Tests are integrated into the build process:

```bash
# Run tests before build
yarn test && yarn build

# Run tests in CI/CD
yarn test:ci && yarn build:ci
```

### Test Configuration

- **Vitest** - Unit and integration tests
- **Playwright** - End-to-end testing
- **Storybook** - Visual component testing
- **Jest** - Additional testing utilities

## 🔍 Troubleshooting

### Common Build Issues

**TypeScript Errors:**

```bash
# Clear TypeScript cache
rm -rf packages/*/tsconfig.tsbuildinfo

# Rebuild with clean slate
yarn clean && yarn build
```

**Dependency Issues:**

```bash
# Check for version mismatches
yarn deps:check

# Align all package versions
yarn align-versions

# Reinstall dependencies
```

**Build Performance:**

```bash
# Profile build performance
yarn build --profile

# Check bundle size
yarn analyze

# Clear build cache
yarn clean
```

### Debug Mode

Enable debug output for troubleshooting:

```bash
# Verbose build output
DEBUG=* yarn build

# TypeScript compiler verbose
yarn test --verbose

# Detailed test output
yarn test --verbose
```

## 📊 Build Monitoring

### Performance Metrics

Track build performance over time:

- **Build time** - Total time to build all packages
- **Bundle size** - Size of final bundles
- **Test coverage** - Percentage of code covered by tests
- **Type checking time** - Time to validate all types

### Quality Gates

Automated quality checks in the build process:

- **Zero TypeScript errors** - All types must be valid
- **90%+ test coverage** - Maintain high test coverage
- **No linting errors** - Code must pass all lint rules
- **Bundle size limits** - Prevent bundle bloat

### Continuous Integration

Build pipeline for automated testing and deployment:

```yaml
# Example CI configuration
- name: Install dependencies

- name: Type check
  run: yarn test

- name: Run tests
  run: yarn test:ci

- name: Build packages
  run: yarn build

- name: Deploy
```

## 🔧 Advanced Configuration

### Custom Build Scripts

Create custom build scripts for specific needs:

```bash
# Development build with specific features
yarn build:dev:features

# Production build with analytics
yarn build:prod:analytics

# Static build for hosting platforms
yarn build:static
```

### Environment Variables

Configure builds with environment variables:

```bash
# Development build
NODE_ENV=development yarn build

# Production build
NODE_ENV=production yarn build

# Static site build
BUILD_TARGET=static yarn build
```

### Build Hooks

Add custom logic to build process:

```json
{
  "scripts": {
    "prebuild": "yarn clean",
    "build": "yarn build:packages",
    "postbuild": "yarn analyze"
  }
}
```

---

**Related Documentation:**

- [Development Guide](../DEVELOPMENT.md) - Development workflow and tools
- [Architecture Guide](../ARCHITECTURE.md) - System design and structure
- [Getting Started](../GETTING_STARTED.md) - Initial setup and installation
