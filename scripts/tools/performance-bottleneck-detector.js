#!/usr/bin/env node

/**
 * Performance Bottleneck Detector for ADHD Trading Dashboard
 * 
 * Identifies performance bottlenecks and optimization opportunities:
 * 1. Heavy components and expensive operations
 * 2. Re-render frequency analysis
 * 3. Bundle size and code splitting opportunities
 * 4. Memory usage patterns
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import chalk from 'chalk';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const DASHBOARD_SRC = path.join(__dirname, '../../packages/dashboard/src');
const SHARED_SRC = path.join(__dirname, '../../packages/shared/src');

/**
 * Get all files for performance analysis
 */
function getPerformanceFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    if (!fs.existsSync(currentDir)) return;
    
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.')) {
        traverse(fullPath);
      } else if (item.match(/\.(tsx?|jsx?)$/) && !item.includes('.test.')) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

/**
 * Analyze performance characteristics of a file
 */
function analyzePerformance(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const stats = fs.statSync(filePath);
    const relativePath = path.relative(process.cwd(), filePath);
    
    return {
      file: relativePath,
      size: stats.size,
      type: getFileType(filePath, content),
      complexity: analyzeComplexity(content),
      renderingConcerns: analyzeRenderingConcerns(content),
      memoryUsage: analyzeMemoryUsage(content),
      bundleImpact: analyzeBundleImpact(content),
      optimizations: suggestOptimizations(content),
      severity: calculateSeverity(content, stats.size)
    };
  } catch (error) {
    console.warn(`Error analyzing ${filePath}: ${error.message}`);
    return null;
  }
}

/**
 * Determine file type for performance analysis
 */
function getFileType(filePath, content) {
  if (content.includes('export default') && content.includes('return')) {
    return 'component';
  }
  if (filePath.includes('/hooks/') || content.includes('use')) {
    return 'hook';
  }
  if (filePath.includes('/services/') || content.includes('class')) {
    return 'service';
  }
  if (filePath.includes('/utils/')) {
    return 'utility';
  }
  return 'other';
}

/**
 * Analyze code complexity
 */
function analyzeComplexity(content) {
  const lines = content.split('\n').length;
  const functions = (content.match(/function|=>/g) || []).length;
  const conditionals = (content.match(/if|switch|ternary|\?/g) || []).length;
  const loops = (content.match(/for|while|map|filter|reduce/g) || []).length;
  const jsxElements = (content.match(/<\w+/g) || []).length;
  
  return {
    lines,
    functions,
    conditionals,
    loops,
    jsxElements,
    cyclomaticComplexity: functions + conditionals + loops,
    score: Math.round((lines / 10) + functions + (conditionals * 2) + (loops * 3) + jsxElements)
  };
}

/**
 * Analyze rendering concerns
 */
function analyzeRenderingConcerns(content) {
  const concerns = [];
  let score = 0;
  
  // Check for expensive operations in render
  if (content.includes('JSON.parse') && !content.includes('useMemo')) {
    concerns.push('JSON.parse in render without memoization');
    score += 3;
  }
  
  if (content.includes('sort(') && !content.includes('useMemo')) {
    concerns.push('Array sorting without memoization');
    score += 2;
  }
  
  if (content.includes('filter(') && !content.includes('useMemo')) {
    concerns.push('Array filtering without memoization');
    score += 2;
  }
  
  // Check for missing optimization hooks
  const useStateCount = (content.match(/useState\(/g) || []).length;
  const useCallbackCount = (content.match(/useCallback\(/g) || []).length;
  const useMemoCount = (content.match(/useMemo\(/g) || []).length;
  
  if (useStateCount > 3 && useCallbackCount === 0) {
    concerns.push('Multiple useState without useCallback');
    score += 2;
  }
  
  if (content.includes('map(') && useMemoCount === 0) {
    concerns.push('Array mapping without memoization');
    score += 1;
  }
  
  // Check for inline object/array creation
  const inlineObjects = (content.match(/\{\s*\w+:/g) || []).length;
  const inlineArrays = (content.match(/\[\s*\w+/g) || []).length;
  
  if (inlineObjects > 5) {
    concerns.push('Frequent inline object creation');
    score += 1;
  }
  
  if (inlineArrays > 3) {
    concerns.push('Frequent inline array creation');
    score += 1;
  }
  
  return {
    concerns,
    score,
    optimizationHooks: {
      useCallback: useCallbackCount,
      useMemo: useMemoCount,
      useRef: (content.match(/useRef\(/g) || []).length
    }
  };
}

/**
 * Analyze memory usage patterns
 */
function analyzeMemoryUsage(content) {
  const issues = [];
  let score = 0;
  
  // Check for potential memory leaks
  if (content.includes('addEventListener') && !content.includes('removeEventListener')) {
    issues.push('Event listeners without cleanup');
    score += 3;
  }
  
  if (content.includes('setInterval') && !content.includes('clearInterval')) {
    issues.push('Intervals without cleanup');
    score += 3;
  }
  
  if (content.includes('setTimeout') && !content.includes('clearTimeout')) {
    issues.push('Timeouts without cleanup');
    score += 2;
  }
  
  // Check for large data structures
  if (content.includes('new Array(') || content.includes('Array.from(')) {
    issues.push('Large array creation');
    score += 1;
  }
  
  if (content.includes('new Map(') || content.includes('new Set(')) {
    issues.push('Map/Set usage - monitor size');
    score += 1;
  }
  
  return {
    issues,
    score
  };
}

/**
 * Analyze bundle impact
 */
function analyzeBundleImpact(content) {
  const imports = [];
  let score = 0;
  
  // Extract imports
  const importRegex = /import\s+.*from\s+['"`]([^'"`]+)['"`]/g;
  let match;
  while ((match = importRegex.exec(content)) !== null) {
    imports.push(match[1]);
  }
  
  // Check for heavy dependencies
  const heavyDeps = [
    'lodash', 'moment', 'axios', 'react-router-dom', 'styled-components',
    'recharts', 'd3', 'chart.js', 'three', 'babylonjs'
  ];
  
  const heavyImports = imports.filter(imp => 
    heavyDeps.some(dep => imp.includes(dep))
  );
  
  score += heavyImports.length * 2;
  
  // Check for dynamic imports
  const dynamicImports = (content.match(/import\(/g) || []).length;
  score -= dynamicImports; // Dynamic imports are good for bundle splitting
  
  return {
    totalImports: imports.length,
    heavyImports,
    dynamicImports,
    score
  };
}

/**
 * Suggest performance optimizations
 */
function suggestOptimizations(content) {
  const suggestions = [];
  
  if (content.includes('useState') && !content.includes('useCallback')) {
    suggestions.push('Add useCallback for event handlers');
  }
  
  if (content.includes('map(') && !content.includes('useMemo')) {
    suggestions.push('Memoize expensive array operations');
  }
  
  if (content.includes('JSON.parse')) {
    suggestions.push('Memoize JSON parsing operations');
  }
  
  if ((content.match(/<\w+/g) || []).length > 20 && !content.includes('memo')) {
    suggestions.push('Consider React.memo for component optimization');
  }
  
  if (content.includes('useEffect') && content.includes('[]')) {
    suggestions.push('Review useEffect dependencies');
  }
  
  if (content.includes('filter(') || content.includes('sort(')) {
    suggestions.push('Consider virtualization for large lists');
  }
  
  return suggestions;
}

/**
 * Calculate severity score
 */
function calculateSeverity(content, fileSize) {
  let severity = 0;
  
  // File size impact
  if (fileSize > 50000) severity += 3; // >50KB
  else if (fileSize > 20000) severity += 2; // >20KB
  else if (fileSize > 10000) severity += 1; // >10KB
  
  // Complexity impact
  const lines = content.split('\n').length;
  if (lines > 500) severity += 3;
  else if (lines > 200) severity += 2;
  else if (lines > 100) severity += 1;
  
  // Performance patterns
  if (content.includes('useState') && !content.includes('useCallback')) severity += 1;
  if (content.includes('JSON.parse') && !content.includes('useMemo')) severity += 2;
  if (content.includes('addEventListener') && !content.includes('removeEventListener')) severity += 2;
  
  return Math.min(severity, 10); // Cap at 10
}

/**
 * Identify critical bottlenecks
 */
function identifyBottlenecks(analyses) {
  const bottlenecks = analyses
    .filter(Boolean)
    .filter(analysis => analysis.severity >= 5)
    .sort((a, b) => b.severity - a.severity);
  
  return bottlenecks.map(analysis => ({
    ...analysis,
    priority: analysis.severity >= 8 ? 'CRITICAL' : 
              analysis.severity >= 6 ? 'HIGH' : 'MEDIUM',
    impact: calculateImpact(analysis)
  }));
}

/**
 * Calculate performance impact
 */
function calculateImpact(analysis) {
  let impact = 0;
  
  // Rendering impact
  impact += analysis.renderingConcerns.score * 2;
  
  // Memory impact
  impact += analysis.memoryUsage.score * 1.5;
  
  // Bundle impact
  impact += analysis.bundleImpact.score;
  
  // Complexity impact
  impact += analysis.complexity.score / 10;
  
  return Math.round(impact);
}

/**
 * Display results
 */
function displayResults(analyses, bottlenecks) {
  console.log(chalk.blue('⚡ PERFORMANCE BOTTLENECK ANALYSIS'));
  console.log(chalk.blue('==================================\n'));
  
  const validAnalyses = analyses.filter(Boolean);
  
  // Summary
  console.log(chalk.yellow('📊 SUMMARY:'));
  console.log(`   Total Files Analyzed: ${validAnalyses.length}`);
  console.log(`   Critical Bottlenecks: ${bottlenecks.filter(b => b.priority === 'CRITICAL').length}`);
  console.log(`   High Priority Issues: ${bottlenecks.filter(b => b.priority === 'HIGH').length}`);
  console.log(`   Medium Priority Issues: ${bottlenecks.filter(b => b.priority === 'MEDIUM').length}\n`);
  
  // File size analysis
  const largFiles = validAnalyses
    .filter(a => a.size > 10000)
    .sort((a, b) => b.size - a.size)
    .slice(0, 10);
  
  if (largFiles.length > 0) {
    console.log(chalk.yellow('📁 LARGEST FILES:'));
    console.log('------------------');
    largFiles.forEach((analysis, index) => {
      console.log(`${index + 1}. ${analysis.file}`);
      console.log(`   Size: ${(analysis.size / 1024).toFixed(1)}KB`);
      console.log(`   Type: ${analysis.type}`);
      console.log(`   Complexity: ${analysis.complexity.score}\n`);
    });
  }
  
  // Critical bottlenecks
  const criticalBottlenecks = bottlenecks.filter(b => b.priority === 'CRITICAL');
  if (criticalBottlenecks.length > 0) {
    console.log(chalk.red('🚨 CRITICAL BOTTLENECKS:'));
    console.log('-------------------------');
    criticalBottlenecks.forEach((bottleneck, index) => {
      console.log(`${index + 1}. ${bottleneck.file}`);
      console.log(`   Priority: ${bottleneck.priority}`);
      console.log(`   Severity: ${bottleneck.severity}/10`);
      console.log(`   Impact: ${bottleneck.impact}`);
      console.log(`   Size: ${(bottleneck.size / 1024).toFixed(1)}KB`);
      
      if (bottleneck.renderingConcerns.concerns.length > 0) {
        console.log(`   Rendering Issues:`);
        bottleneck.renderingConcerns.concerns.forEach(concern => {
          console.log(`     - ${concern}`);
        });
      }
      
      if (bottleneck.memoryUsage.issues.length > 0) {
        console.log(`   Memory Issues:`);
        bottleneck.memoryUsage.issues.forEach(issue => {
          console.log(`     - ${issue}`);
        });
      }
      console.log('');
    });
  }
  
  // High priority bottlenecks
  const highPriorityBottlenecks = bottlenecks.filter(b => b.priority === 'HIGH');
  if (highPriorityBottlenecks.length > 0) {
    console.log(chalk.yellow('⚠️  HIGH PRIORITY ISSUES:'));
    console.log('-------------------------');
    highPriorityBottlenecks.slice(0, 5).forEach((bottleneck, index) => {
      console.log(`${index + 1}. ${bottleneck.file}`);
      console.log(`   Severity: ${bottleneck.severity}/10`);
      console.log(`   Primary Issues: ${bottleneck.renderingConcerns.concerns.slice(0, 2).join(', ')}`);
      console.log('');
    });
  }
  
  // Optimization opportunities
  const optimizationOpportunities = validAnalyses
    .filter(a => a.optimizations.length > 0)
    .sort((a, b) => b.optimizations.length - a.optimizations.length)
    .slice(0, 10);
  
  if (optimizationOpportunities.length > 0) {
    console.log(chalk.green('💡 OPTIMIZATION OPPORTUNITIES:'));
    console.log('-------------------------------');
    optimizationOpportunities.forEach((analysis, index) => {
      console.log(`${index + 1}. ${analysis.file}`);
      analysis.optimizations.slice(0, 3).forEach(opt => {
        console.log(`   - ${opt}`);
      });
      console.log('');
    });
  }
  
  // Bundle analysis
  const bundleImpact = validAnalyses
    .filter(a => a.bundleImpact.heavyImports.length > 0)
    .sort((a, b) => b.bundleImpact.score - a.bundleImpact.score);
  
  if (bundleImpact.length > 0) {
    console.log(chalk.cyan('📦 BUNDLE IMPACT ANALYSIS:'));
    console.log('---------------------------');
    bundleImpact.slice(0, 5).forEach((analysis, index) => {
      console.log(`${index + 1}. ${analysis.file}`);
      console.log(`   Heavy Imports: ${analysis.bundleImpact.heavyImports.join(', ')}`);
      console.log(`   Total Imports: ${analysis.bundleImpact.totalImports}`);
      console.log(`   Dynamic Imports: ${analysis.bundleImpact.dynamicImports}\n`);
    });
  }
}

/**
 * Main analysis function
 */
export async function analyzePerformanceBottlenecks() {
  console.log(chalk.blue('🔍 Analyzing Performance Bottlenecks...\n'));
  
  // Get all files
  const allFiles = [
    ...getPerformanceFiles(DASHBOARD_SRC),
    ...getPerformanceFiles(SHARED_SRC)
  ];
  
  console.log(`📁 Found ${allFiles.length} files to analyze\n`);
  
  // Analyze each file
  const analyses = allFiles.map(analyzePerformance);
  
  // Identify bottlenecks
  const bottlenecks = identifyBottlenecks(analyses);
  
  // Display results
  displayResults(analyses, bottlenecks);
  
  return {
    analyses: analyses.filter(Boolean),
    bottlenecks
  };
}

// Export for use in other tools
export { analyzePerformanceBottlenecks as default };

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  analyzePerformanceBottlenecks().catch(console.error);
}
