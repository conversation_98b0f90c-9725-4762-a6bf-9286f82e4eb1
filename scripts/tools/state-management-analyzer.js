#!/usr/bin/env node

/**
 * State Management Analyzer for ADHD Trading Dashboard
 * 
 * Analyzes state management patterns and efficiency:
 * 1. React Context usage and optimization
 * 2. State update patterns and performance
 * 3. Hook usage and custom hook opportunities
 * 4. State persistence and synchronization
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import chalk from 'chalk';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const DASHBOARD_SRC = path.join(__dirname, '../../packages/dashboard/src');
const SHARED_SRC = path.join(__dirname, '../../packages/shared/src');

/**
 * Get all relevant files for state analysis
 */
function getStateFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    if (!fs.existsSync(currentDir)) return;
    
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.')) {
        traverse(fullPath);
      } else if (item.match(/\.(tsx?|jsx?)$/) && !item.includes('.test.')) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

/**
 * Analyze state management in a file
 */
function analyzeStateManagement(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = path.relative(process.cwd(), filePath);
    
    return {
      file: relativePath,
      type: getFileType(filePath, content),
      statePatterns: extractStatePatterns(content),
      hookUsage: extractHookUsage(content),
      contextUsage: extractContextUsage(content),
      performance: analyzeStatePerformance(content),
      complexity: calculateStateComplexity(content)
    };
  } catch (error) {
    console.warn(`Error analyzing ${filePath}: ${error.message}`);
    return null;
  }
}

/**
 * Determine file type for state analysis
 */
function getFileType(filePath, content) {
  if (filePath.includes('/context/') || content.includes('createContext')) {
    return 'context';
  }
  if (filePath.includes('/hooks/') || filePath.includes('use')) {
    return 'hook';
  }
  if (filePath.includes('/state/') || content.includes('reducer')) {
    return 'state';
  }
  if (content.includes('useState') || content.includes('useReducer')) {
    return 'component';
  }
  return 'other';
}

/**
 * Extract state patterns
 */
function extractStatePatterns(content) {
  return {
    useState: (content.match(/useState\(/g) || []).length,
    useReducer: (content.match(/useReducer\(/g) || []).length,
    useContext: (content.match(/useContext\(/g) || []).length,
    createContext: (content.match(/createContext\(/g) || []).length,
    useSelector: (content.match(/useSelector\(/g) || []).length,
    useStore: (content.match(/useStore\(/g) || []).length,
    dispatch: (content.match(/dispatch\(/g) || []).length,
    setState: (content.match(/set\w+\(/g) || []).length
  };
}

/**
 * Extract hook usage patterns
 */
function extractHookUsage(content) {
  const hooks = {
    useEffect: (content.match(/useEffect\(/g) || []).length,
    useCallback: (content.match(/useCallback\(/g) || []).length,
    useMemo: (content.match(/useMemo\(/g) || []).length,
    useRef: (content.match(/useRef\(/g) || []).length,
    custom: []
  };
  
  // Extract custom hooks
  const customHookRegex = /use[A-Z]\w+/g;
  let match;
  while ((match = customHookRegex.exec(content)) !== null) {
    if (!['useState', 'useEffect', 'useCallback', 'useMemo', 'useRef', 'useContext', 'useReducer'].includes(match[0])) {
      hooks.custom.push(match[0]);
    }
  }
  
  return hooks;
}

/**
 * Extract context usage patterns
 */
function extractContextUsage(content) {
  const contexts = [];
  
  // Find context providers
  const providerRegex = /(\w+)\.Provider/g;
  let match;
  while ((match = providerRegex.exec(content)) !== null) {
    contexts.push({
      name: match[1],
      type: 'provider'
    });
  }
  
  // Find context consumers
  const consumerRegex = /useContext\((\w+)\)/g;
  while ((match = consumerRegex.exec(content)) !== null) {
    contexts.push({
      name: match[1],
      type: 'consumer'
    });
  }
  
  return contexts;
}

/**
 * Analyze state performance implications
 */
function analyzeStatePerformance(content) {
  const issues = [];
  
  // Check for potential performance issues
  if (content.includes('useState') && !content.includes('useCallback')) {
    const stateCount = (content.match(/useState\(/g) || []).length;
    if (stateCount > 3) {
      issues.push('Multiple useState without useCallback - potential re-render issues');
    }
  }
  
  if (content.includes('useEffect') && content.includes('[]')) {
    const emptyDepsCount = (content.match(/useEffect\([^,]+,\s*\[\]/g) || []).length;
    if (emptyDepsCount > 2) {
      issues.push('Multiple useEffect with empty deps - consider consolidation');
    }
  }
  
  if (content.includes('JSON.parse') && content.includes('localStorage')) {
    issues.push('Synchronous localStorage parsing - consider async patterns');
  }
  
  if ((content.match(/dispatch\(/g) || []).length > 5) {
    issues.push('High dispatch frequency - consider batching updates');
  }
  
  return {
    issues,
    optimizations: suggestOptimizations(content)
  };
}

/**
 * Suggest performance optimizations
 */
function suggestOptimizations(content) {
  const suggestions = [];
  
  if (content.includes('useState') && !content.includes('useMemo')) {
    suggestions.push('Consider useMemo for expensive calculations');
  }
  
  if (content.includes('useContext') && !content.includes('memo')) {
    suggestions.push('Consider React.memo for context consumers');
  }
  
  if (content.includes('useSelector') && !content.includes('shallowEqual')) {
    suggestions.push('Consider shallow equality for selectors');
  }
  
  return suggestions;
}

/**
 * Calculate state complexity score
 */
function calculateStateComplexity(content) {
  const stateOperations = (content.match(/useState|useReducer|dispatch|setState/g) || []).length;
  const effectOperations = (content.match(/useEffect|useCallback|useMemo/g) || []).length;
  const contextOperations = (content.match(/useContext|createContext/g) || []).length;
  
  return {
    stateOperations,
    effectOperations,
    contextOperations,
    total: stateOperations + effectOperations + contextOperations
  };
}

/**
 * Analyze state architecture patterns
 */
function analyzeStateArchitecture(analyses) {
  const architecture = {
    contexts: new Map(),
    hooks: new Map(),
    patterns: {
      contextProviders: 0,
      customHooks: 0,
      stateManagers: 0
    },
    recommendations: []
  };
  
  analyses.forEach(analysis => {
    if (analysis) {
      // Count contexts
      analysis.contextUsage.forEach(ctx => {
        if (!architecture.contexts.has(ctx.name)) {
          architecture.contexts.set(ctx.name, { providers: 0, consumers: 0 });
        }
        const context = architecture.contexts.get(ctx.name);
        if (ctx.type === 'provider') context.providers++;
        if (ctx.type === 'consumer') context.consumers++;
      });
      
      // Count custom hooks
      analysis.hookUsage.custom.forEach(hook => {
        architecture.hooks.set(hook, (architecture.hooks.get(hook) || 0) + 1);
      });
      
      // Count patterns
      if (analysis.type === 'context') architecture.patterns.contextProviders++;
      if (analysis.type === 'hook') architecture.patterns.customHooks++;
      if (analysis.statePatterns.useReducer > 0) architecture.patterns.stateManagers++;
    }
  });
  
  // Generate architecture recommendations
  const totalComponents = analyses.filter(a => a && a.type === 'component').length;
  const contextCount = architecture.contexts.size;
  
  if (contextCount < 3 && totalComponents > 20) {
    architecture.recommendations.push({
      type: 'ARCHITECTURE',
      issue: 'Low context usage with many components',
      solution: 'Consider more React Context for shared state management'
    });
  }
  
  if (architecture.patterns.customHooks < 5 && totalComponents > 15) {
    architecture.recommendations.push({
      type: 'REUSABILITY',
      issue: 'Low custom hook usage',
      solution: 'Extract common state logic into custom hooks'
    });
  }
  
  return architecture;
}

/**
 * Display results
 */
function displayResults(analyses, architecture) {
  console.log(chalk.blue('🧠 STATE MANAGEMENT ANALYSIS'));
  console.log(chalk.blue('=============================\n'));
  
  // Summary
  const validAnalyses = analyses.filter(Boolean);
  console.log(chalk.yellow('📊 SUMMARY:'));
  console.log(`   Total Files Analyzed: ${validAnalyses.length}`);
  console.log(`   Context Providers: ${architecture.patterns.contextProviders}`);
  console.log(`   Custom Hooks: ${architecture.patterns.customHooks}`);
  console.log(`   State Managers: ${architecture.patterns.stateManagers}\n`);
  
  // State pattern breakdown
  const patternTotals = validAnalyses.reduce((totals, analysis) => {
    Object.keys(analysis.statePatterns).forEach(pattern => {
      totals[pattern] = (totals[pattern] || 0) + analysis.statePatterns[pattern];
    });
    return totals;
  }, {});
  
  console.log(chalk.yellow('🎯 STATE PATTERN USAGE:'));
  Object.entries(patternTotals).forEach(([pattern, count]) => {
    console.log(`   ${pattern}: ${count} occurrences`);
  });
  console.log('');
  
  // Context analysis
  if (architecture.contexts.size > 0) {
    console.log(chalk.green('🔗 CONTEXT USAGE:'));
    console.log('------------------');
    architecture.contexts.forEach((usage, name) => {
      console.log(`${name}:`);
      console.log(`   Providers: ${usage.providers}`);
      console.log(`   Consumers: ${usage.consumers}`);
      console.log(`   Ratio: ${usage.consumers}:${usage.providers}\n`);
    });
  }
  
  // Custom hooks
  if (architecture.hooks.size > 0) {
    console.log(chalk.blue('🪝 CUSTOM HOOKS:'));
    console.log('----------------');
    const sortedHooks = Array.from(architecture.hooks.entries())
      .sort((a, b) => b[1] - a[1]);
    
    sortedHooks.slice(0, 10).forEach(([hook, count]) => {
      console.log(`${hook}: used ${count} times`);
    });
    console.log('');
  }
  
  // Performance issues
  const performanceIssues = validAnalyses
    .filter(a => a.performance.issues.length > 0)
    .slice(0, 10);
  
  if (performanceIssues.length > 0) {
    console.log(chalk.red('⚠️  PERFORMANCE CONCERNS:'));
    console.log('-------------------------');
    performanceIssues.forEach(analysis => {
      console.log(`${analysis.file}:`);
      analysis.performance.issues.forEach(issue => {
        console.log(`   - ${issue}`);
      });
      console.log('');
    });
  }
  
  // High complexity files
  const highComplexity = validAnalyses
    .filter(a => a.complexity.total > 10)
    .sort((a, b) => b.complexity.total - a.complexity.total)
    .slice(0, 5);
  
  if (highComplexity.length > 0) {
    console.log(chalk.yellow('🔥 HIGH STATE COMPLEXITY:'));
    console.log('--------------------------');
    highComplexity.forEach(analysis => {
      console.log(`${analysis.file}:`);
      console.log(`   State Operations: ${analysis.complexity.stateOperations}`);
      console.log(`   Effect Operations: ${analysis.complexity.effectOperations}`);
      console.log(`   Context Operations: ${analysis.complexity.contextOperations}`);
      console.log(`   Total Complexity: ${analysis.complexity.total}\n`);
    });
  }
  
  // Recommendations
  if (architecture.recommendations.length > 0) {
    console.log(chalk.green('💡 ARCHITECTURE RECOMMENDATIONS:'));
    console.log('----------------------------------');
    architecture.recommendations.forEach((rec, index) => {
      console.log(`${index + 1}. [${rec.type}] ${rec.issue}`);
      console.log(`   Solution: ${rec.solution}\n`);
    });
  }
  
  // Optimization suggestions
  const optimizations = validAnalyses
    .filter(a => a.performance.optimizations.length > 0)
    .slice(0, 5);
  
  if (optimizations.length > 0) {
    console.log(chalk.cyan('⚡ OPTIMIZATION OPPORTUNITIES:'));
    console.log('------------------------------');
    optimizations.forEach(analysis => {
      console.log(`${analysis.file}:`);
      analysis.performance.optimizations.forEach(opt => {
        console.log(`   - ${opt}`);
      });
      console.log('');
    });
  }
}

/**
 * Main analysis function
 */
export async function analyzeStateManagement() {
  console.log(chalk.blue('🔍 Analyzing State Management Patterns...\n'));
  
  // Get all relevant files
  const allFiles = [
    ...getStateFiles(DASHBOARD_SRC),
    ...getStateFiles(SHARED_SRC)
  ];
  
  console.log(`📁 Found ${allFiles.length} files to analyze\n`);
  
  // Analyze each file
  const analyses = allFiles.map(analyzeStateManagement);
  
  // Analyze overall architecture
  const architecture = analyzeStateArchitecture(analyses);
  
  // Display results
  displayResults(analyses, architecture);
  
  return {
    analyses: analyses.filter(Boolean),
    architecture
  };
}

// Export for use in other tools
export { analyzeStateManagement as default };

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  analyzeStateManagement().catch(console.error);
}
